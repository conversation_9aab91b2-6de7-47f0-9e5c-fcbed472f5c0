export interface GenerateInput {
  id?: GenerateId;
  preset: string;
  query: string;
  query_args?: QueryArgs;
  prompt_params?: PromptParams;
  model_params?: ModelParams;
}

export interface GenerateId {
  /** Identificador persistente del cliente */
  clt?: string;
  /** Identificador de sesión */
  ses?: string;
  /** Correlación para una interacción multi-paso */
  corr?: string;
}

export interface QueryArgs {
  /** ID para indicar que la query viene de una sugerencia previa */
  query_id?: string;

  /** Campos para inyectar en preamble/template */
  fields?: Record<string, string | number | boolean | null>;

  /**
   * Medios (solo para modelos multimodales).
   * Debe llevar **o** url **o** data (no ambos).
   */
  media?: Media;
}

/** Media: exactamente uno de los dos */
export type Media = { url: string } | { data: string };
// url  -> URL desde la que descargar la imagen
// data -> cadena base64 (puede ser Data URI)

export interface PromptParams {
  /** Sobrescribe el system prompt */
  preamble?: string | string[];

  /** Ejemplos a añadir/sobrescribir */
  examples?: string[];

  /** Plantilla para el mensaje de usuario */
  template?: string;
}

export interface ModelParams {
  /** Tamaño máximo de salida */
  max_tokens?: number;

  /** 0..2 */
  temperature?: number;

  /** 0..1 */
  top_p?: number;

  /** Extensiones específicas del proveedor */
  [key: string]: unknown;
}
